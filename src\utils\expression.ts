export const expression: Record<string, RegExp> = {
  /**
  普通车牌 | 新能源货车 | 新能源小客车 车牌校验规则;
	 * 
	 */
  carPlate:
    /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$)|([A-Z0-9]{5}[DF]$)|([DF][A-Z0-9]{5}$)|([A-Z0-9]{5}挂$))/,
  // 车牌号
  plate:
    /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$)|([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,
  // 座机
  linePhone: /^(0\d{2,3}-)?([2-9]\d{6,7})+(-\d{1,6})?$/,
  // 移动电话
  mobilePhone: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
  // 移动电话或座机
  mobileOrLinePhone:
    /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$|^(0\d{2,3}-)?([2-9]\d{6,7})+(-\d{1,6})?$/,
  // 正整数
  positiveInt: /(^\+?[1-9][0-9]*$)|(^\d{1}$)/,
  /**/
  // 邮箱
  email: /^[`~!#$%^&*._\-+=0-9a-zA-Z]+@[a-zA-Z0-9]+(.[a-zA-Z0-9]{2,})+$/,
  // 统一社会信用代码
  credit: /^[1-9A-GY]{1}[1239]{1}[1-5]{1}[0-9]{5}[0-9A-Z]{10}$/,
  // 邮编
  postCode: /^[1-9]\d{5}$/,
  // 网址
  webUrl: /^((http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-.,@?^=%&:/~+#]*[\w\-@?^=%&/~+#])?)$/,
  // 经度
  longitude:
    /^(-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,15})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,15}|180)$/,
  // 纬度
  latitude: /^(-|\+)?([0-8]?\d{1}\.\d{0,15}|90\.0{0,15}|[0-8]?\d{1}|90)$/,
  // 身份证号
  IDCard:
    /(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)|(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)/,
  // 正数（可以输入小数点）
  positive: /^[+]{0,1}(\d+)$|^[+]{0,1}(\d+\.\d+)$/,
};

export default expression;
