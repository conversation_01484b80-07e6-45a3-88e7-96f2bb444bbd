import { isEmpty, YTHLocalization } from 'yth-ui';

export const LANGUAGE_STORE_KEY: string = '$_language';

export const CurrentUser: () => Record<string, string> = () => {
  // return JSON.parse(window.sessionStorage.getItem('$_user') || '{}');
  return {
    id: 'b0a4e0b6b68be5a1d10856508668a7e5',
    username: 'admin',
    realName: '超级管理员',
    accountId: '********',
    unitId: '********',
    tenantId: 1,
    unitCode: '********',
    unitName: '中华人民共和国',
    unitType: '-2',
  };
};

export const Token: () => string = () => {
  // return window.sessionStorage.getItem('$_token') || '';
  return 'bearer bb3d17b3-fe77-43e6-aeee-108bc24e16c4';
};

export const Setting: () => Record<string, string> = () => {
  return JSON.parse(window.localStorage.getItem('yth_form_config_setting') || '{}');
};

/**
 * 配置request请求时的默认参数
 */
export const ConstHeaders: () => Headers = () => {
  const { affinityHost = '' } = Setting();
  const headers: Headers = new Headers();
  headers.append('Content-Language', YTHLocalization.getLanguage());
  if (Token()) {
    headers.append('Authorization', Token() || '');
  }
  if (!isEmpty(affinityHost)) {
    headers.append('affinity_host', affinityHost);
  }
  return headers;
};

/**
 * 配置request请求时的默认参数 - 返回普通对象
 */
export const ConstHeadersObject: () => Record<string, string> = () => {
  const { affinityHost = '' } = Setting();
  const headers: Record<string, string> = {};
  headers['Content-Language'] = YTHLocalization.getLanguage();
  if (Token()) {
    headers.Authorization = Token() || '';
  }
  if (!isEmpty(affinityHost)) {
    headers.affinity_host = affinityHost;
  }
  return headers;
};
