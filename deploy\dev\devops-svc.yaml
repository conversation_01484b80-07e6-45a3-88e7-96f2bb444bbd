apiVersion: v1
kind: Service
metadata:
  labels:
    app: CIP
    component: campus-front-inspection
  name: campus-front-inspection
  namespace: campus
spec:
  ports:
    - name: http-80
      port: 80
      protocol: TCP
      targetPort: 80
      #30000-32767
      nodePort: 30019
  selector:
    app: CIP
    component: campus-front-inspection
    tier: backend
  sessionAffinity: None
  type: NodePort
