import React, { useState, useRef, useEffect } from 'react';
import { Button, Col, Empty, Row, Spin } from 'antd';
import { InspectionTaskLineVo } from '@/types/task';
import style from './index.module.less';
import MapDraw from '../MapDraw/index';
import { MapCardExpose, MapPoint } from '../MapDraw/types';

type Props = {
  inspectionLineList: InspectionTaskLineVo[];
  planLinePoints: string;
};

const LocationWithRadius: React.FC<Props> = ({
  inspectionLineList, // 路线数据
  planLinePoints, // 计划路线
}) => {
  // 地图输入框内容
  const [lineList, setLineList] = useState<InspectionTaskLineVo[]>([]); // 线路数据
  const [pointList, setPointList] = useState<MapPoint[]>([]); // 点位数据
  const [areaList, setAreaList] = useState<string>(''); // 路线数据
  const [activeIndex, setActiveIndex] = useState<number>(-1); // 当前选中的线路索引
  const tmapRef: React.MutableRefObject<MapCardExpose | null> = useRef();

  const [openMap, setOpenMap] = useState<boolean>(false); // 打开地图

  // 打开地图
  const showMap: (index: number, resetMap: boolean) => void = (
    index: number,
    resetMap: boolean,
  ) => {
    setOpenMap(true);
    setActiveIndex(index);
    const list: Record<string, string>[] = [];
    // 线路  包含计划线路和实际线路
    const allLineList: InspectionTaskLineVo[] = [];
    // 计划线路
    if (planLinePoints) {
      const planLine: InspectionTaskLineVo[] = JSON.parse(planLinePoints) || [];
      planLine.forEach((item) => {
        allLineList.push({
          deviceName: '计划线路',
          color: '#00FF00',
          list: item.list,
        });
      });
    }
    // 实际线路
    if (lineList.length > 0 && index < lineList.length) {
      allLineList.push(lineList[index]);
    }
    const points: string = JSON.stringify(allLineList) || '';
    // 点位
    const newList: MapPoint[] = list.map((item: Record<string, string>) => {
      const locationList: string[] = String(item.pointLocation).split(',');
      return {
        x: locationList[0],
        y: locationList[1],
        z: 0,
        label: item.pointName || '',
      };
    });
    setAreaList(points);
    setPointList(newList);
    if (resetMap) {
      tmapRef.current?.resetMap();
    }
  };

  useEffect(() => {
    setAreaList('');
    setLineList([]);
    try {
      if ((inspectionLineList && inspectionLineList.length > 0) || planLinePoints) {
        const processedLineList: InspectionTaskLineVo[] = inspectionLineList.map((item) => ({
          deviceName: item.deviceName,
          color: item.color,
          list: item.pointList,
        }));
        setLineList(processedLineList);
        showMap(0, false);
      }
    } catch {
      // 解析失败时设置为空数组
      setAreaList('');
    }
  }, [inspectionLineList]);

  return (
    <div>
      {lineList.length === 0 && (
        <Empty
          style={{
            width: '100%',
            margin: '20px',
          }}
          imageStyle={{
            height: 60,
          }}
          description={<span>无巡检线路数据</span>}
        />
      )}
      {lineList.length > 0 && (
        <Row>
          <Col span={3}>
            {lineList.map((item, index) => (
              <div key={item.deviceName} style={{ width: '100%' }}>
                <Button
                  className={style['map-select-line-btn']}
                  type={activeIndex === index ? 'primary' : 'default'}
                  size="small"
                  style={{ margin: '0 10px 10px 0' }}
                  onClick={() => showMap(index, true)}
                >
                  {item.deviceName}
                </Button>
              </div>
            ))}
          </Col>
          <Col span={21}>
            <div className={style['location-select-container']}>
              <Spin spinning={false}>
                {openMap && (
                  <div className={style['map-locationradiusmodal-content']}>
                    <div className={style['map-area']}>
                      <MapDraw
                        ref={tmapRef}
                        operateType="view"
                        areaList={areaList}
                        pointList={pointList}
                        mapType="line"
                      />
                    </div>
                  </div>
                )}
              </Spin>
            </div>
          </Col>
        </Row>
      )}
    </div>
  );
};

export default LocationWithRadius;
