import { QueryParams } from '@/types/statistics';
import { <PERSON><PERSON>, But<PERSON>, message } from 'antd';
import React, { useRef, useMemo } from 'react';
import YTHList, { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import TaskApi from '@/service/taskApi';
import dicParams from '@/pages/InspectionPlan/dicParams';
import baseApi from '@/service/baseApi';
import { TaskPageQueryParam, TaskPageResponse, TaskQueryParam, TaskVo } from '@/types/task';
import moment, { Moment } from 'moment';
import style from '../statistics.module.less';
/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗传入的数据 */
  searchParams: QueryParams;
  /** 弹窗是否可见 */
  visible: boolean;
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
  /** 任务状态 */
  taskStatus: string;
};

/**
 * @description  任务列表 弹窗
 * @param PropsTypes PropsTypes
 */
const TaskListDialog: React.FC<PropsTypes> = ({
  searchParams,
  visible,
  closeModal = () => {},
  taskStatus,
}) => {
  // 使用 useMemo 确保 listAction 只创建一次
  const listAction: ActionType = useMemo(() => YTHList.createAction(), []);
  const listActionRef: React.MutableRefObject<ActionType | undefined> =
    useRef<ActionType>(listAction);

  // 表格列配置
  const columns: IYTHColumnProps[] = useMemo(
    () => [
      { dataIndex: 'serialNo', title: '序号', width: 80, display: false },
      { dataIndex: 'taskCode', title: '任务编码', query: true, display: true },
      {
        dataIndex: 'taskName',
        title: '任务名称',
        query: true,
        display: true,
        componentProps: {
          placeholder: '名称关键词',
        },
      },
      { dataIndex: 'planName', title: '所属计划', width: 100, query: false },
      { dataIndex: 'inspectionMethodText', title: '巡检方式', width: 120, query: false },
      { dataIndex: 'taskTypeText', title: '任务类型', width: 100, query: false },
      {
        dataIndex: 'inspectionMethod',
        title: '巡检方式',
        width: 0,
        query: true,
        display: true,
        componentName: 'Selector',
        componentProps: {
          p_props: {
            allowClear: true,
            placeholder: '选择巡检方式',
          },
          request: async () => {
            return (await baseApi.getDictionary(dicParams.INSPECTION_METHOD)) ?? [];
          },
        },
      },
      {
        dataIndex: 'taskType',
        title: '任务类型',
        width: 0,
        query: true,
        display: true,
        componentName: 'Selector',
        componentProps: {
          p_props: {
            allowClear: true,
            placeholder: '选择任务类型',
          },
          request: async () => {
            return (await baseApi.getDictionary(dicParams.PLAN_TYPE)) ?? [];
          },
        },
      },
      {
        dataIndex: 'taskStatus',
        title: '任务状态',
        width: 0,
        query: true,
        display: true,
        componentName: 'Selector',
        componentProps: {
          p_props: {
            allowClear: true,
            placeholder: '选择任务状态',
          },
          request: async () => {
            return (await baseApi.getDictionary(dicParams.TASK_STATUS)) ?? [];
          },
        },
      },
      { dataIndex: 'directorUserName', title: '负责人', width: 90, query: false, display: true },
      { dataIndex: 'taskStatusText', title: '任务状态', width: 100, query: false, display: true },
      {
        dataIndex: 'planStartTime',
        title: '计划开始时间',
        width: 150,
        query: false,
        display: true,
      },
      { dataIndex: 'planEndTime', title: '计划结束时间', width: 150, query: false, display: true },
      {
        dataIndex: 'actualStartTime',
        title: '实际开始时间',
        width: 150,
        query: false,
        display: true,
      },
      {
        dataIndex: 'actualEndTime',
        title: '实际结束时间',
        width: 150,
        query: false,
        display: true,
      },
      {
        dataIndex: 'startDate',
        title: '计划开始时间',
        width: 0,
        queryMode: 'group',
        display: false,
        query: true,
        componentName: 'DatePicker',
        componentProps: {
          placeholder: '请输入',
          precision: `day`,
          formatter: `YYYY-MM-DD`,
        },
      },
    ],
    [],
  );

  // 处理弹窗关闭的回调函数
  const handleCloseModal: () => void = () => {
    closeModal();
  };

  /** 处理查询参数：从选择器数组中提取第一个选项的 code 值 */
  const handleFilter: (f: TaskQueryParam) => TaskQueryParam = (f: TaskQueryParam) => {
    const filter: TaskQueryParam = f || {};
    if (f.taskName && f.taskName !== '') {
      filter.taskName = f.taskName;
    }
    if (f.inspectionMethod && Array.isArray(f.inspectionMethod) && f.inspectionMethod.length > 0) {
      filter.inspectionMethod = f.inspectionMethod[0].code;
    }
    if (f.taskType && Array.isArray(f.taskType) && f.taskType.length > 0) {
      filter.taskType = f.taskType[0].code;
    }
    if (f.directorUserName && f.directorUserName !== '') {
      filter.directorUserName = f.directorUserName;
    }
    if (f.taskStatus && Array.isArray(f.taskStatus) && f.taskStatus.length > 0) {
      filter.taskStatus = f.taskStatus[0].code;
    }
    if (!f.taskStatus && taskStatus) {
      filter.taskStatus = taskStatus;
    }
    if (f.isRemind && Array.isArray(f.isRemind) && f.isRemind.length > 0) {
      filter.isRemind = f.isRemind[0].code;
    }
    if (f.startDate_start && f.startDate_start !== '') {
      filter.startDate = f.startDate_start;
    }
    if (f.startDate_end && f.startDate_end !== '') {
      filter.endDate = f.startDate_end;
    }
    return filter;
  };
  return (
    <Modal
      width="80%"
      title="任务列表"
      style={{ top: 30 }}
      visible={visible}
      destroyOnClose
      onCancel={handleCloseModal}
      maskClosable={false}
      footer={[
        <Button key="cancel" onClick={handleCloseModal} className={style['reset-btn']}>
          取消
        </Button>,
      ]}
    >
      <YTHList
        defaultQuery={{
          startDate_start: searchParams?.startTime,
          startDate_end: searchParams?.endTime,
          taskStatus,
        }}
        code="InspectionTaskList"
        action={listAction}
        actionRef={listActionRef}
        showRowSelection={false}
        extraOperation={[]}
        operation={[]}
        listKey="id"
        request={async (filter, pagination, sort) => {
          try {
            const convertFieldName: (field: string) => string = (field: string) =>
              field.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();

            let descColumns: string = 'create_date';
            let ascColumns: string = '';

            if (sort?.order && sort.field) {
              const convertedField: string = convertFieldName(sort.field);
              if (sort.order === 'desc') {
                descColumns = convertedField;
              } else if (sort.order === 'asc') {
                ascColumns = convertedField;
              }
            }
            const queryParams: TaskPageQueryParam = {
              descs: [descColumns],
              aescs: [ascColumns],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            };
            const resData: TaskPageResponse = await TaskApi.queryByPage(queryParams);
            if (resData.code && resData.code === 200) {
              const dataWithSerialNo: (TaskVo & { serialNo: number })[] = resData.data.map(
                (item: TaskVo, index: number) => ({
                  ...item,
                  serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
                }),
              );
              return {
                data: dataWithSerialNo,
                total: resData.total,
                success: true,
              };
            }
            message.error('请求数据出错，请刷新重试或联系管理员');
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch {
            message.error('请求数据出错，请刷新重试或联系管理员');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        columns={columns}
      />
    </Modal>
  );
};

export default TaskListDialog;
