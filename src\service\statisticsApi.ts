import { inspectionRequest } from '@/request';
import { BaseResponse } from '@/types/common';
import {
  InspectionAlarmStatisticsVo,
  InspectionSumVo,
  InspectionTaskEchartsLineDataVo,
  QueryParams,
} from '@/types/statistics';

export default {
  /**
   * 获取合计值
   * @param data QueryParams 统计查询参数
   * @returns 合计值
   */
  querySumData: async (data: QueryParams): Promise<BaseResponse<InspectionSumVo>> => {
    return inspectionRequest.post(`/inspectionStatistics/countSum`, { data });
  },
  /**
   * @description 按天统计
   * @param data 统计查询参数
   * @returns  按天统计数据结果
   */
  trendDayTask: async (
    data: QueryParams,
  ): Promise<BaseResponse<InspectionTaskEchartsLineDataVo>> => {
    return inspectionRequest.post(`/inspectionStatistics/trendDayTask`, { data });
  },
  /**
   * 告警来源占比统计
   * @param data
   * @returns 告警来源占比统计结果
   */
  getAlarmSourcePie: async (
    data: QueryParams,
  ): Promise<BaseResponse<InspectionAlarmStatisticsVo[]>> => {
    return inspectionRequest.post(`/inspectionStatistics/alarmSourcePie`, { data });
  },
};
