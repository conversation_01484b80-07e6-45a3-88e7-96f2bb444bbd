module.exports = {
  env: {
    browser: true,
    es2021: true,
  },
  extends: [
    'airbnb',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:prettier/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ['react', '@typescript-eslint', 'prettier'],
  rules: {
    'react/jsx-filename-extension': ['error', { extensions: ['.tsx', '.jsx'] }],
    'prettier/prettier': 'error',
    'react/function-component-definition': [
      'error',
      {
        namedComponents: 'arrow-function',
        unnamedComponents: 'arrow-function',
      },
    ],
    'import/no-webpack-loader-syntax': 'off',
    'react/button-has-type': 'warn',
    'import/extensions': [
      'error',
      'ignorePackages',
      {
        ts: 'never',
        tsx: 'never',
        js: 'never',
        jsx: 'never',
      },
    ],
    '@typescript-eslint/no-explicit-any': 'error',
    'import/no-extraneous-dependencies': [
      'error',
      {
        devDependencies: ['webpack.config.js', 'webpack.*.js'],
        peerDependencies: true,
      },
    ],
    'react/require-default-props': 'off',
    '@typescript-eslint/typedef': [
      'error',
      {
        variableDeclaration: true,
        variableDeclarationIgnoreFunction: false,
        memberVariableDeclaration: true,
        arrowParameter: false,
        parameter: true,
        propertyDeclaration: true,
      },
    ],
    '@typescript-eslint/no-var-requires': 'off', // 允许 require
    '@typescript-eslint/no-require-imports': 'off',
  },
  settings: {
    'import/resolver': {
      typescript: {}, // 让 import/no-unresolved 能识别 ts 路径
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
    },
    react: {
      version: 'detect',
    },
  },
  overrides: [
    {
      files: ['build.plugin.js'],
      rules: {
        '@typescript-eslint/typedef': 'off',
      },
    },
  ],
};
