/* 组件根容器 - 加载状态包装器 */
.spin-wrap {
  width: 100%;
  height: 100%;

  :global(.ant-spin-container) {
    width: 100%;
    height: 100%;
  }
}

/* 隐藏 Cesium 全屏按钮 */
:global(.cesium-viewer-fullscreen-container) {
  display: none !important;
}

/* 覆盖 Ant Design Card 样式 */
:global(.ant-card-body) {
  padding-top: 0;
}

/* 地图主容器 */
.map-container {
  height: 100%;
  width: 100%;
  position: relative;
}

/* 工具栏容器 */
.toolbar {
  display: flex;
  justify-content: flex-start;
  padding: 10px;
  flex-wrap: wrap;
  z-index: 1;
  position: absolute;
  pointer-events: none;
  top: 0;
  left: 0;
  right: 0;
}

/* 输入栏 */
.input-bar {
  margin-top: 12px;
  margin-left: 32px;
  z-index: 1;
  pointer-events: all;
}

/* 结果面板 */
.result-panel {
  width: 320px;
  max-height: 350px;
  display: flex;
  flex-direction: column;
  margin-bottom: 10px;
  pointer-events: all;

  /* 当作为 Ant Card 使用时的样式 */
  &:global(.ant-card) {
    background-color: rgb(255 255 255 / 0.1);

    :global(.ant-select-selector) {
      background-color: rgb(255 255 255 / 0.7);
    }

    :global(.ant-card-head) {
      padding: 0 12px;
    }

    :global(.ant-card-head-title) {
      padding: 12px 0;
    }
  }
}

/* 搜索栏 */
.search-bar {
  display: flex;
  align-items: center;
}

/* 搜索栏文本 */
.search-bar-text {
  text-overflow: ellipsis;
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
}

/* 加载中的结果列表 */
.result-list-loading {
  min-height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;

  &:global(.ant-list-loading) {
    min-height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

/* 信息面板 */
.info {
  padding: 12px 12px 0;
  background-color: rgb(255 255 255 / 0);
  border-radius: 2px;
  border-width: 0;
  border-style: solid;
  border-color: transparent;
  pointer-events: all;

  /* 覆盖 Ant Design Input 样式 */
  :global(.ant-input) {
    background-color: rgb(255 255 255 / 0.9);
    height: 40px;
    border-radius: 5px;
  }

  /* 数字输入框样式重置 */
  input[type="number"] {
    appearance: textfield;

    &::-webkit-inner-spin-button,
    &::-webkit-outer-spin-button {
      appearance: none;
      margin: 0;
    }
  }
}

/* 信息项 */
.info-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

/* 项目容器 */
.item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

/* 信息面板内的搜索栏 */
.info-search-bar {
  justify-content: space-around;
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 搜索栏输入区域 */
.search-bar-input {
  display: flex;
  justify-content: space-between;
}

/* 输入区域 */
.input-area {
  min-width: 250px;
}

/* 地图搜索按钮 */
.map-search-btn {
  margin-left: 5px;
}

/* 地图清除按钮 */
.map-clear-btn {
  margin-left: 5px;
}

/* 搜索结果显示区域 */
.search-bar-result {
  position: absolute;
  width: 30%;
  top: 40px;
  left: 30px;
  background-color: rgb(0 0 0 / 0.6);
  margin-bottom: 10px;
  padding-left: 10px;
  text-align: left;
  color: #fff;
}

/* 编辑按钮 */
.edit {
  &:hover {
    cursor: pointer;
  }
}

/* 颜色指示器 */
.color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

/* 地址文本 */
.address-text {
  margin-bottom: 0;
  color: rgb(0 0 0 / 1);
  max-width: 210px;
}

/* 颜色选择区域 */
.color-area {
  z-index: 1;
  left: 20%;
  bottom: 20px;
  position: absolute;
  display: flex;
  align-items: center;
}

/* 重置按钮 */
.reset-button {
  margin-left: 10px;
  height: 100%;
}

/* 按钮区域 */
.button-area {
  height: 200px;
  width: 60px;
  z-index: 1;
  bottom: 0;
  right: 20px;
  position: absolute;
}

/* 通用按钮样式 */
.button-use {
  width: 60px;
  height: 60px;
  border-radius: 60px;
  text-align: center;
  line-height: 60px;
  margin-top: 20px;

  &:hover {
    cursor: pointer;
  }
}

/* 取消按钮 */
.cancel-button {
  background-color: rgb(221 197 41 / 1);
}

/* 确认按钮 */
.confirm-button {
  background-color: rgb(22 155 213 / 1);
  color: aliceblue;
}

/* 地图弹窗容器 */
.map-popup-container {
  width: 60vw;
  height: 60vh;
}
