.modal-operation {
  text-align: right;
  margin-top: 20px;
}
// 任务表单标题
.task-form-title {
  border-left: 3px solid #1890ff;
  margin-bottom: 10px;
  padding-left: 8px;
  font-weight: bold;
  font-size: 12px;
}

.add-task-moduel {
  :global(.ant-modal-body) {
    padding: 0 0 0 10px;
  }
}

/* 任务详情弹窗样式 允许垂直滚动 */
.yth-inspection-moduel {
  position: relative;
  height: calc(100vh - 180px);
  overflow: hidden auto;
  padding-right: 10px;
  box-sizing: border-box;
  margin-top: 10px;
}

.actual-end-time{
  margin: 10px 0;

}

/* 在task.module.less中 */
.custom-label {
  width: 40px !important;
  min-width: 40px !important;
}

.task-form{
  border-left: 1px solid #e4e4e4;
  border-right: 1px solid #e4e4e4;
  height: 100%;

  :global(.ant-form-item-label){
    font-size: 12px;
    background-color: #f2f2f2;

    >label{
      font-size: 12px;
      line-height: 34px !important;
      height: 34px !important;
    }
  }

  :global(.ant-form-item){
    margin-bottom: 0;
    line-height: 34px;
    height: 100% !important;
  }
  // 输入框控件字体大小
  :global(.ant-input) {
    font-size: 12px;
    line-height: 34px;
    border: 0;
  }
  // 选择器控件字体大小
  :global(.ant-select-selection-item) {
    font-size: 12px;
    line-height: 34px;
    border: 0;
  }
  // 日期选择器控件字体大小
   :global(.ant-picker) {
    border: 0 !important;
  }

  :global(.ant-picker-input > input) {
    font-size: 12px;
    line-height: 34px;
    border: 0;
  }
  // placeholder字体大小
  :global(.ant-input::placeholder) {
    font-size: 12px;
    line-height: 34px;
  }
  // 下拉框样式
  :global(.ant-select) {
    display: block;
  }

  :global(.ant-select .ant-select-selector) {
    font-size: 12px;
    height: 34px !important;
    line-height: 34px !important;
    border: 0;
  }

  :global(.ant-select .ant-select-selection-search-input) {
    font-size: 12px;
    height: 34px !important;
    line-height: 34px;
  }

  :global(.ant-select .ant-select-selection-placeholder) {
    font-size: 12px;
    height: 34px !important;
    line-height: 34px !important;
  }
  // 下拉框选择后字体大小
  :global(.ant-select .ant-select-selection-item) {
    font-size: 12px;
    height: 34px !important;
    line-height: 34px !important;
  }
  // 多选下拉框选择后字体大小
  :global(.ant-select.ant-select-multiple .ant-select-selection-item) {
    font-size: 12px;
    height: 28px !important;
    line-height: 28px !important;
  }
  // 多选下拉框输入框高度
  :global(.ant-select-selection-overflow-item) {
    height: 34px !important;
  }
  // 下拉框选择后字体大小
  :global(.ant-select .actual-end-time > input) {
    font-size: 12px;
    height: 34px !important;
    line-height: 34px !important;
    border: 0;
  }

  :global(.ant-select-selection-placeholder) {
    font-size: 12px;
    line-height: 34px;
  }
  // 日期选择器placeholder字体大小
  :global(.ant-picker-input > input::placeholder) {
    font-size: 12px;
    line-height: 34px;
  }
  // 外层row边框
  :global( > .ant-row) {
    border-top: 1px solid #e4e4e4;
    height: 36px;
    overflow: hidden;
  }
  // form-label 高度
  .lable-form-item{
    :global(.ant-form-item-label){
      text-align: right;
      font-size: 12px;
      margin: 0;
      height: 32px;
      overflow: hidden;

      :global(>label){
        padding-right: 10px;
      }
    }
  }
  // 居中label
  .center-label-form-item{
    :global(.ant-form-item-label){
      text-align: center;
      font-size: 12px;
      margin: 0;
      height: 32px;
      overflow: hidden;

      :global(>label){
        padding: 0;
      }

      :global(>label::after){
        margin: 0;
      }
    }
  }

  .lable-col{
    height: 36px;
    background-color: #f2f2f2;
    position: relative;
  }
  // 禁用输入框背景色
  :global(.ant-input[disabled]){
    background-color: #fff;
  }

  :global(.ant-select-disabled .ant-select-selector){
    background-color: #fff !important;
  }

  :global(.ant-picker-input > input[disabled]){
    background-color: #fff !important;
  }

  :global(.ant-picker.ant-picker-disabled){
    background-color: #fff !important;
  }

  :global(.ant-select-disabled.ant-select-multiple .ant-select-selection-item){
    background-color: #fff !important;
  }

  .required-item{
    background-color: #e5f2f8 !important;

    :global(.ant-select-selector){
      background-color: #e5f2f8 !important;
    }

    :global(.ant-select-selection-placeholder){
      color: #007fba;
    }
  }

  .required-item::placeholder{
    color: #007fba !important;
  }

}
