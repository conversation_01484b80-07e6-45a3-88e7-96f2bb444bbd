export interface History {
  length: number;
  action: Action;
  location: Location;
  push<T>(path: string, state?: T): void;
  replace<T>(path: string, state?: T): void;
  go(n: number): void;
  back(): void;
  forward(): void;
  listen(listener: (update: Update) => void): () => void;
  block(blocker: Blocker): () => void;
}

// 通用响应
export interface BaseResponse<T> {
  code: number;
  msg: string;
  data: T;
  success: boolean;
}
export interface PageResponse<T> {
  code: number;
  msg?: string;
  data: T[];
  total?: number;
  size?: number;
  current?: number;
  pages?: number;
  success: boolean;
}
/**
 * @description 分页查询参数
 */
export interface PageQueryParam<T> {
  descs: string[];
  aescs: string[];
  condition: T;
  currentPage: number;
  pageSize: number;
}
