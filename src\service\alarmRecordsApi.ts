import { inspectionRequest } from '@/request';
import downloadBlob from '@/service/downloadBlob';
import { AlarmRecordsPageQueryParam, AlarmRecordsPageResponse } from '@/types/alarmRecords';

export default {
  /**
   * @param data PageQueryParam 分页查询参数
   * @returns  巡检告警记录信息列表 || null
   * @description 获取列表
   */
  queryByPage: async (data: AlarmRecordsPageQueryParam) => {
    return inspectionRequest.post<AlarmRecordsPageResponse>(`/alarmRecords/queryByPage`, {
      data,
    });
  },
  /**
   * @param data PageQueryParam 分页查询参数
   * @returns  巡检告警记录信息导出文件 || null
   * @description 导出列表
   */
  exportByPage: async (data: AlarmRecordsPageQueryParam) => {
    return new Promise((resolve) => {
      downloadBlob('/alarmRecords/exportByPage', {
        method: 'POST',
        data,
      }).finally(() => {
        resolve('finally');
      });
    });
  },
};
