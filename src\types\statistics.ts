/**
 * 巡检计划、任务、异常处理记录合计数量Vo
 */
export interface InspectionSumVo {
  /** 巡检计划总数 */
  totalPlanNum?: number;
  /** 巡检任务总数 */
  totalTaskNum?: number;
  /** 未完成巡检任务总数 */
  notFinishedTaskNum?: number;
  /** 已完成巡检任务总数 */
  finishedTaskNum?: number;
  /** 异常数量 */
  totalExceptionNum?: number;
  /** 未处置异常数量 */
  notHandleExceptionNum?: number;
  /** 报警数量 */
  totalAlarmNum?: number;
  /** 未处置报警数量 */
  notHandleAlarmNum?: number;
}

/**
 * 巡检任务执行趋势 VO 适用于 Echarts 趋势图 格式数据封装
 */
export interface InspectionTaskEchartsLineDataVo {
  /** x轴的名称 */
  xaxisData: string[];
  /** 多条线趋势，不同类别趋势名称 */
  lineNames: string[];
  /** 计划任务数量（总任务数量） */
  totalTaskValues: number[];
  /** 已完成任务总数 */
  finishedTaskValues: number[];
  /** 发现异常的任务数量 */
  exceptionTaskValues: number[];
}

/**
 * 趋势图数据结构
 */
export interface TrendDayData {
  xaxisData: string[];
  lineNames: string[];
  yaxisData: {
    [key: string]: number[];
  };
}

/**
 * 巡检报警统计 结果VO
 */
export interface InspectionAlarmStatisticsVo {
  /** 总告警数量 */
  totalNum?: number;
  /** 告警来源 */
  alarmSource?: string;
}

/**
 * 统计查询参数
 */
export interface QueryParams {
  startTime?: string;
  endTime?: string;
}
