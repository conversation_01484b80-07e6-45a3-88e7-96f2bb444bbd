import { QueryParams } from '@/types/statistics';
import { <PERSON><PERSON>, But<PERSON>, message } from 'antd';
import React, { useEffect, useRef, useMemo } from 'react';
import YTHList, { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import AlarmRecordsApi from '@/service/alarmRecordsApi';
import dicParams from '@/pages/InspectionPlan/dicParams';
import baseApi from '@/service/baseApi';
import {
  AlarmRecordsPageQueryParam,
  AlarmRecordsPageResponse,
  AlarmRecordsQueryParam,
  AlarmRecordsResultVo,
} from '@/types/alarmRecords';
import style from '../statistics.module.less';

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗传入的数据 */
  searchParams: QueryParams;
  /** 弹窗是否可见 */
  visible: boolean;
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
  /** 报警处置状态 */
  handleStatus: string;
};

/**
 * @description  任务列表 弹窗
 * @param PropsTypes PropsTypes
 */
const AlarmRecordsListDialog: React.FC<PropsTypes> = ({
  searchParams,
  visible,
  closeModal = () => {},
  handleStatus,
}) => {
  // 使用 useMemo 确保 listAction 只创建一次
  const listAction: ActionType = useMemo(() => YTHList.createAction(), []);
  const listActionRef: React.MutableRefObject<ActionType | undefined> =
    useRef<ActionType>(listAction);

  // 表格列配置
  const columns: IYTHColumnProps[] = useMemo(
    () => [
      { dataIndex: 'serialNo', title: '序号', width: 80, display: false },
      {
        dataIndex: 'startDate',
        title: '报警发生时间',
        width: 0,
        queryMode: 'group',
        display: false,
        query: true,
        componentName: 'DatePicker',
        componentProps: {
          placeholder: '请输入',
          precision: `day`,
          formatter: `YYYY-MM-DD`,
        },
      },
      {
        dataIndex: 'alarmSource',
        title: '报警来源',
        width: 0,
        query: true,
        display: true,
        componentName: 'Selector',
        componentProps: {
          p_props: {
            allowClear: true,
            placeholder: '报警来源',
          },
          request: () => {
            return [
              { text: '无人机', code: '无人机' },
              { text: '智能安全帽', code: '智能安全帽' },
            ];
          },
        },
      },
      { dataIndex: 'alarmDeviceCode', title: '报警设备编号', query: true, display: true },
      { dataIndex: 'alarmDeviceName', title: '报警设备名称', query: true, display: true },
      { dataIndex: 'taskName', title: '任务名称', query: true, display: true },
      {
        dataIndex: 'alarmDescribe',
        title: '报警描述',
        query: true,
        display: true,
      },
      {
        dataIndex: 'handleStatus',
        title: '处置状态',
        width: 100,
        query: true,
        display: true,
        componentName: 'Selector',
        componentProps: {
          p_props: {
            allowClear: true,
            placeholder: '请选择处置状态',
          },
          request: async () => {
            return (await baseApi.getDictionary(dicParams.HANDLE_STATUS)) ?? [];
          },
        },
      },
      { dataIndex: 'handleUserName', title: '处置人', width: 90, query: false, display: true },
      {
        dataIndex: 'handleTime',
        title: '处置时间',
        width: 150,
        query: false,
        display: true,
      },
      { dataIndex: 'handleMethod', title: '处置说明', width: 150, query: false, display: true },
    ],
    [],
  );

  // 处理弹窗关闭的回调函数
  const handleCloseModal: () => void = () => {
    closeModal();
  };

  // 加载数据
  useEffect(() => {}, [searchParams, visible]);

  /** 处理查询参数：从选择器数组中提取第一个选项的 code 值 */
  const handleFilter: (f: AlarmRecordsQueryParam) => AlarmRecordsQueryParam = (
    f: AlarmRecordsQueryParam,
  ) => {
    const filter: AlarmRecordsQueryParam = f || {};
    if (f.alarmSource && f.alarmSource !== '') {
      filter.alarmSource = f.alarmSource;
    }
    if (f.alarmSource && Array.isArray(f.alarmSource) && f.alarmSource.length > 0) {
      filter.alarmSource = f.alarmSource[0].code;
    }
    if (f.handleStatus && Array.isArray(f.handleStatus) && f.handleStatus.length > 0) {
      filter.handleStatus = f.handleStatus[0].code;
    }
    if (!f.handleStatus && handleStatus) {
      filter.handleStatus = handleStatus;
    }
    if (f.reportDeviceCode && f.reportDeviceCode !== '') {
      filter.reportDeviceCode = f.reportDeviceCode;
    }
    if (f.startDate_start && f.startDate_start !== '') {
      filter.alarmTimeStart = f.startDate_start;
    }
    if (f.startDate_end && f.startDate_end !== '') {
      filter.alarmTimeEnd = f.startDate_end;
    }
    return filter;
  };
  return (
    <Modal
      width="80%"
      title="任务列表"
      style={{ top: 30 }}
      visible={visible}
      destroyOnClose
      onCancel={handleCloseModal}
      maskClosable={false}
      footer={[
        <Button key="cancel" onClick={handleCloseModal} className={style['reset-btn']}>
          取消
        </Button>,
      ]}
    >
      <YTHList
        defaultQuery={{}}
        code="InspectionAlarmRecordsList"
        action={listAction}
        actionRef={listActionRef}
        showRowSelection={false}
        extraOperation={[]}
        operation={[]}
        listKey="id"
        request={async (filter, pagination, sort) => {
          try {
            const convertFieldName: (field: string) => string = (field: string) =>
              field.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();

            let descColumns: string = 'create_date';
            let ascColumns: string = '';

            if (sort?.order && sort.field) {
              const convertedField: string = convertFieldName(sort.field);
              if (sort.order === 'desc') {
                descColumns = convertedField;
              } else if (sort.order === 'asc') {
                ascColumns = convertedField;
              }
            }
            const queryParams: AlarmRecordsPageQueryParam = {
              descs: [descColumns],
              aescs: [ascColumns],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            };
            const resData: AlarmRecordsPageResponse =
              await AlarmRecordsApi.queryByPage(queryParams);
            if (resData.code && resData.code === 200) {
              const dataWithSerialNo: (AlarmRecordsResultVo & { serialNo: number })[] =
                resData.data.map((item: AlarmRecordsResultVo, index: number) => ({
                  ...item,
                  serialNo: (pagination.current - 1) * pagination.pageSize + index + 1,
                }));
              return {
                data: dataWithSerialNo,
                total: resData.total,
                success: true,
              };
            }
            message.error('请求数据出错，请刷新重试或联系管理员');
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch {
            message.error('请求数据出错，请刷新重试或联系管理员');
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        columns={columns}
      />
    </Modal>
  );
};

export default AlarmRecordsListDialog;
