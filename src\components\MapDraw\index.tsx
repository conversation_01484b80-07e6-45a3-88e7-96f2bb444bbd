import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Map, PlotDraw, Util } from 'yth-map';
import markImg from '@/assets/position.png';
import locationImg from '@/assets/location.png';
import { Github } from 'react-color/lib/components/github/Github';
import { Button, Spin, message } from 'antd';
import baseApi, { ByParkCodeType } from '@/service/baseApi';
import config from './config';
import { Props, SORN, MapCardExpose, MapPoint, PolygonData } from './types';
import tdt from './images/tdt_img.jpg';
import style from './MapDraw.module.less';

// 地图图层配置类型定义
interface LayerConfig {
  url: string;
  layer: string;
  style: string;
  format: string;
  tileMatrixSetID: string;
  minimumLevel: number;
  maximumLevel: number;
  tilingScheme: number;
}

interface LayerGroup {
  name: string;
  image: string;
  show: boolean;
  list: LayerConfig[];
}

// 搜索结果POI类型
interface SearchPOI {
  name: string;
  lonlat: string;
}

// 搜索结果类型
interface SearchResult {
  pois?: SearchPOI[];
}

// // useDebounce 防抖 Hook
// const useDebounce: <T>(value: T, delay?: number) => T = <T,>(value: T, delay: number = 300): T => {
//   const [debouncedValue, setDebouncedValue] = useState(value);

//   useEffect(() => {
//     const timeout: NodeJS.Timeout = setTimeout(() => setDebouncedValue(value), delay);
//     return () => clearTimeout(timeout);
//   }, [value, delay]);

//   return debouncedValue;
// };

const map: Map = new Map();
// 当前绘制的线
const currentLine: { id: string }[] = [];
// 当前绘制的点位列表标记
const currentPointList: { id: string }[] = [];
// 当前绘制的点
let currentPoint: { id: string } = { id: '' };

// 地图绘制
const MapCard: React.ForwardRefRenderFunction<MapCardExpose, Props> = (props: Props, ref) => {
  const [initLoading, setInitLoading] = useState<boolean>(false);
  const { areaList, operateType, mapType, pointList, positionVal } = props;

  // 地图初始化
  const layers: LayerGroup[] = [
    {
      name: '天地图影像',
      image: tdt,
      show: true,
      list: [
        // 影像
        {
          url: 'http://t0.tianditu.com/img_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'img',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
        // 影像注记
        {
          url: 'http://t0.tianditu.com/cia_c/wmts?tk=c8b337de7f212a5178a3b1aa7ec3856b&service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'cia',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
      ],
    },
    {
      name: '天地图(矢量)',
      image: tdt,
      show: false,
      list: [
        // 矢量
        {
          url: 'http://t0.tianditu.com/vec_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=vec&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'vec',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
        // 矢量注记
        {
          url: 'http://t0.tianditu.com/cva_c/wmts?tk=52fea9a68c4c16c39dd759ab4fce602f&service=wmts&request=GetTile&version=1.0.0&LAYER=cva&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles',
          layer: 'cva',
          style: 'default',
          format: 'image/png',
          tileMatrixSetID: 'c',
          minimumLevel: 1,
          maximumLevel: 18,
          tilingScheme: 2,
        },
      ],
    },
  ];

  // 点位标记
  const [position, setPosition] = useState<SORN[]>(['', '', '']);
  const colorSelectRef: React.MutableRefObject<string> = useRef<string>(''); // 标记当前选中的颜色
  const [colorSelect, setColorSelect] = useState<string>('');
  const [tipLabel, setTipLabel] = useState<string>(
    operateType !== 'view' ? '请选择颜色并开始绘制' : '',
  ); // 设置提示词
  const polygonListRef: React.MutableRefObject<PolygonData[]> = useRef<PolygonData[]>([]); // 记录当前绘制的多边形

  /**
   * @abstract 根据传参绘制区域
   */
  const drawAreaByDefault: () => void = () => {
    // 先清空已有的区域线条
    currentLine.forEach((lineMarker) => {
      if (lineMarker.id) {
        map.layer.clearById(lineMarker.id);
      }
    });
    // 清空数组
    currentLine.length = 0;

    if (areaList && areaList !== '') {
      try {
        const areaListObj: PolygonData[] = JSON.parse(areaList);
        polygonListRef.current = areaListObj;
        areaListObj.forEach((item: PolygonData, index: number) => {
          // 添加多边形并保存返回的 ID
          const lineMarker: { id: string } = map.layer.addPolyline({
            positions: item.list,
            fill: true,
            outlineColor: item.color,
            fillColor: item.color,
          });
          // 将线条 ID 保存到 currentLine 数组中
          currentLine[index] = lineMarker;
        });
      } catch {
        // 无操作
      }
    }
  };

  /**
   * @abstract 根据传参点位绘制所有点
   */
  const drawPointByList: () => void = () => {
    // 先清空已有的点位标记
    currentPointList.forEach((pointMarker) => {
      if (pointMarker.id) {
        map.layer.clearById(pointMarker.id);
      }
    });
    // 清空数组
    currentPointList.length = 0;

    if (pointList && pointList.length > 0) {
      try {
        pointList.forEach((item: MapPoint) => {
          // 添加标记并保存返回的 ID
          const marker: { id: string } = map.layer.addMarker({
            layerName: 'positionSele',
            point: item,
            img: markImg,
            scale: 1,
            offset: { x: 0, y: -15 },
            label: {
              text: item.label,
              fillColor: '#00ff00',
              offset: { x: 16, y: -11 },
              horizontalOrigin: 1,
              nearFarScalar: [500, 1, 5000, 0],
            },
          });
          // 将标记 ID 保存到 currentPointList 数组中
          currentPointList.push(marker);
        });
      } catch {
        // 无操作
      }
    }
  };

  /**
   * @abstract 根据传参当前点位数据绘制点位
   */
  const drawPointByDefault: () => void = () => {
    if (positionVal && positionVal.x) {
      try {
        currentPoint = map.layer.addMarker({
          layerName: 'positionSele',
          point: positionVal,
          img: locationImg,
          scale: 1,
          offset: { x: 0, y: -15 },
          label: {
            text: positionVal.label,
            fillColor: '#00ff00',
            offset: { x: 16, y: -11 },
            horizontalOrigin: 1,
            nearFarScalar: [500, 1, 5000, 0],
          },
        });
      } catch {
        // 无操作
      }
    }
  };

  const initMap: () => Promise<void> = async (): Promise<void> => {
    setInitLoading(true);
    let initPoint: MapPoint = config.mapCenterStr;
    if (positionVal && positionVal.x) {
      initPoint = positionVal;
    } else if (areaList && areaList !== '') {
      try {
        const areaListObj: PolygonData[] = JSON.parse(areaList);
        if (areaListObj && Array.isArray(areaListObj) && areaListObj.length > 0) {
          initPoint = {
            x: areaListObj[0].list[0],
            y: areaListObj[0].list[1],
            z: areaListObj[0].list[2],
          };
        }
      } catch {
        // console.log(err);
      }
    } else if (pointList && pointList.length > 0 && pointList[0].x) {
      initPoint = {
        x: pointList[0]?.x,
        y: pointList[0]?.y,
        z: pointList[0]?.z,
      };
    } else {
      // 获取园区中心点
      try {
        const park: ByParkCodeType = await baseApi.queryByParkCode();
        const mapCenter: MapPoint = park?.center ? JSON.parse(park?.center) : {};
        initPoint = {
          x: mapCenter?.x ?? 0,
          y: mapCenter?.y ?? 0,
          z: 10,
        };
      } catch {
        // 当查询园区失败时  使用默认的地图中心点
        initPoint = config.mapCenterStr;
      }
    }

    map.initMap({
      container: 'map', // 地图承载容器
      sceneModePicker: true, // 二三维选择按钮-显示控制
      sceneModeChose: 3, // 显示模式 三维: 3 二维: 2  默认值: 3
      positionDisplay: true, // 右下角经纬度显示 默认值: true
      compassDisplay: true, // 罗盘显示 默认值: true
      hostAddr: 'http://***************:8096/check',
      components: true,

      // 初始位置,地图重置(replace)时用到
      initPlace: {
        point: initPoint,
      },
      layersPro: layers,
      // 地图重置按钮   默认视角, 罗盘中恢复的视角，默认是中国范围
      defaultView: {
        rect: [112.96100967885242, 28.194319720664925, 112.97098015969033, 28.198415260838136],
      },
      // 完成地图加载
      callback: () => {
        setInitLoading(false);
        drawAreaByDefault();
        drawPointByDefault();
        drawPointByList();
        map.flyObject(initPoint);
        map.setMapBackground('天地图影像');

        map.plotDraw = new PlotDraw({
          map,
          // callback: (geo: any) => {
          //   console.log(geo, 'geo');
          // },
          editable: false,
        });
      },
    });
  };

  // 获取绘制数据
  const getDrawData: () => PolygonData[] = () => {
    return polygonListRef.current;
  };

  useEffect(() => {
    initMap();
  }, []);

  useEffect(() => {
    if (positionVal && positionVal.x) {
      setPosition([positionVal.x, positionVal.y, positionVal.z]);
    }
  }, [positionVal]);

  // 点位列表更新
  useEffect(() => {
    if (pointList && pointList.length > 0 && map.layer) {
      drawPointByList();
    }
  }, [pointList, map]);

  // 区域列表更新
  useEffect(() => {
    if (areaList && areaList !== '' && map.layer) {
      drawAreaByDefault();
    }
  }, [areaList, map]);

  // 处理搜索结果
  const localSearchResult: (result: SearchResult) => void = (result) => {
    const resultsList: HTMLElement | null = document.getElementById('results');
    if (result && result.pois && Array.isArray(result.pois)) {
      const locations: SearchPOI[] = result.pois;
      locations.forEach((location: SearchPOI, index: number) => {
        const listItem: HTMLLIElement = document.createElement('li');
        listItem.textContent = `${index + 1}: ${location.name}`;
        listItem.addEventListener('click', () => {
          // alert(location.lonlat); // Show the inner info
          const nLoc: string[] = location.lonlat.split(',');
          map.flyObject({ x: parseFloat(nLoc[0]), y: parseFloat(nLoc[1]), z: 155 });
        });
        resultsList?.appendChild(listItem);
      });
    } else {
      message.error('无数据');
    }
  };

  // 根据关键字搜索
  const searchLocation: () => Promise<void> = async () => {
    const resultsList: HTMLElement | null = document.getElementById('results');
    if (resultsList) {
      resultsList.innerHTML = '';
    }
    const inputElement: HTMLInputElement | null = document.getElementById(
      'inputSearch',
    ) as HTMLInputElement;
    const value: string = inputElement?.value || '';
    const rUrl: string = `https://api.tianditu.gov.cn/v2/search?postStr={"keyWord":"${value}","level":"11","mapBound":"102.546150,24.396308,103.157679,25.132221","queryType":"1","count":"10","start":"0"}&type=query&tk=${config.mapKey}`;
    fetch(rUrl, {
      referrerPolicy: 'strict-origin-when-cross-origin',
      body: null,
      method: 'GET',
      mode: 'cors',
      headers: {},
      credentials: 'omit',
    })
      .then((res: Response) => {
        // console.log(res);
        return res.json();
      })
      .then((data: SearchResult) => {
        // console.log(data);
        localSearchResult(data);
      });
  };

  // 清空搜索结果
  const clearSearch: () => void = () => {
    const searchInput: HTMLInputElement | null = document.getElementById(
      'inputSearch',
    ) as HTMLInputElement;
    const resultsList: HTMLElement | null = document.getElementById('results') as HTMLElement;
    if (searchInput) searchInput.value = ''; // Clear search input
    if (resultsList) resultsList.innerHTML = ''; // Clear search results
  };

  // 重置地图
  const resetMap: () => void = () => {
    setInitLoading(false);
    setPosition(['', '']);
    map.resetPlace();
  };

  // 卸载地图
  const destroyMap: () => void = () => {
    resetMap();
    map.resetPlace();
  };

  // 绘制区域
  const drawArea: () => void = () => {
    setTipLabel('正在进行绘制');
    map.plotDraw.activate(1, {
      callback: (geo: { geometry: { coordinates: number[][][] } }) => {
        const psList: number[] = [];
        geo.geometry.coordinates[0].forEach((item: number[]) => {
          psList.push(item[0]);
          psList.push(item[1]);
          psList.push(Util.getHeight({ x: item[0], y: item[1], map }));
          // psList.push(item[2]);
        });
        const currentPolygons: PolygonData[] = polygonListRef.current;

        map.layer.addPolyline({
          positions: psList,
          fill: true,
          outlineColor: colorSelectRef.current,
          fillColor: colorSelectRef.current,
        });

        currentPolygons.push({
          list: psList,
          color: colorSelectRef.current,
        });
        polygonListRef.current = currentPolygons;

        map.plotDraw.deactivate();
        setTipLabel('请选择颜色并继续绘制');
      },
    });
  };

  // 获取当前地图选中点
  const getPointData: () => { x: SORN; y: SORN } = () => {
    return {
      x: position[0],
      y: position[1],
    };
  };

  // 转换位置
  const transferPoint: (value: SORN[]) => MapPoint = (value: SORN[]) => {
    const positionNew: MapPoint = {
      x: Number(value[0]),
      y: Number(value[1]),
      z: Number(value[2]) ?? 1,
    };
    return positionNew;
  };

  // 颜色画笔更改
  const colorChange: (color: { hex: string }) => void = (color: { hex: string }) => {
    setColorSelect(color.hex);
    colorSelectRef.current = color.hex;
    drawArea();
  };

  // 绘制标记点位
  const markPositon: (value: MapPoint) => void = (value) => {
    map.layer.clearById(currentPoint.id);
    // 绘制
    currentPoint = map.layer.addMarker({
      layerName: 'positionSele',
      point: value,
      img: locationImg,
      scale: 1,
      offset: { x: 0, y: -15 },
    });
  };

  // 选择位置
  const selectPosition: () => void = () => {
    if (!map || !map.layer || !map.plotDraw) return;
    setPosition([]);
    map.plotDraw.activate(0, {
      callback: (geo: { geometry: { coordinates: number[][][] } }) => {
        setPosition(geo.geometry.coordinates[0][0]);
        markPositon(transferPoint(geo.geometry.coordinates[0][0]));
        // map.flyObject(transferPoint(geo.geometry.coordinates[0][0]));
      },
    });
  };

  useImperativeHandle(ref, () => {
    return {
      resetMap,
      destroyMap,
      getDrawData,
      getPointData,
    };
  });

  // useEffect(() => {
  //   if (!position[0] || !position[1]) return;
  //   map.flyObject(transferPoint(position));
  //   markPositon(transferPoint(position));
  //   onCoordChange!({ lng: position[0], lat: position[1] });
  // }, [useDebounce(position, 200)]);

  // 取消按钮
  const cancelbotton: () => void = () => {
    polygonListRef.current = [];
    currentLine.forEach((item) => {
      map.layer.clearById(item.id);
    });

    setColorSelect('');
  };

  return (
    <Spin spinning={initLoading} size="large" wrapperClassName={style['spin-wrap']}>
      <main className={style['map-container']}>
        <div
          id="map"
          style={{
            width: '100%',
            height: '100%',
          }}
        />
        <div className={style.toolbar}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <div className={`${style.info} ant-card ant-card-bordered`}>
              <div className={style.item}>
                <div className={style['search-bar']}>
                  <div className={style['search-bar-input']}>
                    <input
                      id="inputSearch"
                      className={style['input-area']}
                      placeholder="请输入搜索内容"
                    />
                    <Button
                      type="default"
                      className={style['map-search-btn']}
                      onClick={searchLocation}
                    >
                      搜索
                    </Button>
                    <Button className={style['map-clear-btn']} type="default" onClick={clearSearch}>
                      重置
                    </Button>
                  </div>
                  <div className={style['search-bar-result']}>
                    <div id="results" />
                  </div>
                </div>
                {mapType === 'point' && (
                  <div style={{ display: 'flex', marginLeft: '10px' }}>
                    <Button
                      style={{ marginLeft: '10px' }}
                      size="small"
                      onClick={() => {
                        selectPosition();
                      }}
                      disabled={operateType === 'view'}
                      type="primary"
                    >
                      选择位置
                    </Button>
                  </div>
                )}
                {mapType === 'line' && (
                  <div style={{ display: 'flex', marginLeft: '10px' }}>
                    {colorSelect && colorSelect !== '' && (
                      <div className={style.color} style={{ backgroundColor: colorSelect }} />
                    )}
                    <div style={{ marginLeft: '20px', color: 'red' }}>{tipLabel}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
        {operateType !== 'view' && mapType === 'line' && (
          <div className={style['color-area']}>
            <Github
              triangle="hide"
              colors={[
                'rgba(255,0,0,0.6)',
                'rgba(255,197,61,0.6)',
                'rgba(0,0,255,0.6)',
                'rgba(0,255,0,0.6)',
              ]}
              styles={{ input: { display: 'none' } }}
              onChange={(color: { hex: string }) => {
                colorChange(color);
              }}
            />
            <Button
              className={style['reset-button']}
              type="default"
              onClick={() => {
                cancelbotton();
              }}
            >
              清空
            </Button>
          </div>
        )}
      </main>
    </Spin>
  );
};

// 设置组件的 displayName 用于调试和开发工具
MapCard.displayName = 'MapCard';

export default React.forwardRef(MapCard);
