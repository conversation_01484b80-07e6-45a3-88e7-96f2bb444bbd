import { PageQueryParam, PageResponse } from '@/types/common';
import { Moment } from 'moment';

/**
 * 任务检查点信息返回结果
 */
export interface TaskPointsVo {
  /** 主键 */
  id?: string;
  /** 任务id */
  taskId?: string;
  /** 检查点名称 */
  pointName?: string;
  /** 检查点坐标 */
  pointLocation?: string;
  /** 巡检内容 */
  inspectionContent?: string;
  /** 检查状态（0: 未检查，1: 已检查） */
  checkStatus?:
    | number
    | { code?: number; text?: string; id?: string; value?: number; lable?: string }[];
  /** 检查结果（检查结果 1：正常 0:异常） */
  checkResult?:
    | number
    | { code?: number; text?: string; id?: string; value?: number; lable?: string }[];
  /** 异常是否需要处置 0：不需要处置 1：需要处置 */
  isNeedHandle?:
    | number
    | { code?: number; text?: string; id?: string; value?: number; lable?: string }[];
  /** 异常描述 */
  exceptionDescribe?: string;
  /** 处置人 */
  handleUserId?: string;
  handleUserIdText?: string;
  /** 处置人名称 */
  handleUserName?: string | { id?: string; name?: string }[];
  /** 处置期限(处置截止时间) */
  handleDeadlineTime?: string;
  /** 处置要求 */
  handleRequire?: string;
  /** 结果描述 */
  checkResultDescription?: string;
  /** 结果照片（图片URL） */
  attachments?: string;
  /** 创建时间 */
  createDate?: string;
  /** 更新时间 */
  updateDate?: string;
}
/**
 * 巡检任务设备信息
 */
export interface TaskDeviceVo {
  /** 主键 */
  id?: string;
  /** 任务id */
  taskId?: string;
  /** 设备类型 */
  deviceType?: string;
  /** 设备id */
  deviceId?: string;
  /** 设备编码 */
  deviceCode?: string;
  /** 设备名称 */
  deviceName?: string;
  /** 备注 */
  remark?: string;
}
/**
 * 巡检线路信息
 */
export interface InspectionTaskLineVo {
  /** 设备名称(或编码) */
  deviceName?: string;
  /** 线路颜色 */
  color?: string;
  /** 线路经纬度列表，必须是[经度，纬度，经度，纬度……]格式 */
  pointList?: string[] | number[];
  list?: string[] | number[];
}
/**
 * 巡检任务信息
 */
export interface TaskVo {
  /** 主键 */
  id?: string;
  /** 任务编码 */
  taskCode?: string;
  /** 任务名称 */
  taskName?: string;
  /** 计划id */
  planId?: string;
  /** 计划名称 */
  planName?: string;
  /** 巡检方式 */
  inspectionMethod?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  /** 任务类型 */
  taskType?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  /** 负责人id */
  directorUserId?: string;
  /** 负责人名称 */
  directorUserName?: string;
  /** 负责人电话 */
  directorPhone?: string;
  /** 任务状态 */
  taskStatus?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  /** 计划开始时间 */
  planStartTime?: string | Date | Moment;
  /** 计划结束时间 */
  planEndTime?: string | Date | Moment;
  /** 实际开始时间 */
  actualStartTime?: string | Date | Moment;
  /** 实际结束时间 */
  actualEndTime?: string | Date | Moment;
  /** 备注 */
  remark?: string;
  /** 参与巡检人 */
  partyUserIds?: string;
  /** 参与巡检人名称 */
  partyUserNames?: string;
  /** 创建时间 */
  createDate?: string | Date | Moment;
  /** 更新时间 */
  updateDate?: string | Date | Moment;
  /** 是否发送短信提醒 (0: 不发送, 1: 需要发送) */
  isRemind?: number | { code?: number; text?: string; id?: string }[];
  /** 任务检查点列表 */
  taskPointsList?: TaskPointsVo[];
  /** 任务设备列表 */
  taskDevicesList?: TaskDeviceVo[];
  /** 任务设备id列表 */
  taskDevicesIds?: string | string[];
  /** 巡检线路列表 */
  inspectionLineList?: InspectionTaskLineVo[];
  /** 视频监控 */
  videos?: string;
  /** 计划线路 */
  planLinePoints?: string;
}
/**
 * 计划检查点查询参数
 */
export interface TaskQueryParam {
  taskName?: string;
  inspectionMethod?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  taskType?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  directorUserName?: string;
  taskStatus?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  isRemind?: number | { code?: number; text?: string; id?: string }[];
  startDate?: string;
  endDate?: string;
  startDate_end?: string;
  startDate_start?: string;
}

/**
 * @description 新增参数
 * @param data 新增参数
 */
export interface TaskInsertParam extends TaskVo {
  id?: string;
  taskId?: string;
}
/**
 * @description 开始参数
 * @param data 开始参数
 */
export interface TaskStartParam extends TaskVo {
  id?: string;
  taskId?: string;
}
/**
 * @description 结束参数
 * @param data 结束参数
 */
export interface TaskEndParam extends TaskVo {
  id?: string;
}
/**
 * @description 更新参数
 * @param data 更新参数
 */
export interface TaskUpdateParam extends TaskVo {
  id?: string;
}
/**
 * @description 分页返回结果
 * @param data 分页查询参数
 */
export interface TaskPageResponse extends PageResponse<TaskVo> {
  data: TaskVo[];
}

/**
 * @description 分页查询参数
 * @param data 分页查询参数
 */
export interface TaskPageQueryParam extends PageQueryParam<TaskQueryParam> {
  condition: TaskQueryParam;
}
