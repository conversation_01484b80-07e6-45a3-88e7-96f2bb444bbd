import { inspectionRequest } from '../request';

export default async (
  url: string,
  { method = 'get', params = {}, data = {} } = {},
  fileName = 'download',
) => {
  let downloadName: string = fileName;

  // 后端获取文件名
  inspectionRequest.interceptors.response.use(async (response) => {
    const contentDisposition: string | null = response.headers.get('content-disposition');

    downloadName = contentDisposition ? window.decodeURI(contentDisposition.split('=')[1]) : '';

    return response;
  });

  const content: BlobPart = await inspectionRequest(url, { method, params, data });

  const blob: Blob = new Blob([content]);
  const reader: FileReader = new FileReader();

  reader.readAsDataURL(blob);

  reader.onload = (e) => {
    const a: HTMLAnchorElement = document.createElement('a');

    a.download = downloadName;
    a.href = e.target.result as string;

    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };
};
