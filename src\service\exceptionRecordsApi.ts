import { inspectionRequest } from '@/request';
import downloadBlob from '@/service/downloadBlob';
import { BaseResponse } from '@/types/common';

import {
  ExceptionRecordsVo,
  ExceptionRecordsInsertParam,
  ExceptionRecordsPageQueryParam,
  ExceptionRecordsPageResponse,
  ExceptionRecordsUpdateParam,
} from '@/types/exceptionRecords';

export default {
  /**
   * @param data InsertParam 待新增的巡检异常记录信息
   * @returns 新增结果 || null
   * @description 新增
   */
  insert: async (data: ExceptionRecordsInsertParam) => {
    return inspectionRequest.post<BaseResponse<object>>(`/exceptionRecords/insert`, { data });
  },
  /**
   * @param data updateParam 待更新的巡检异常记录信息
   * @returns 更新结果 || null
   * @description 修改
   */
  update: async (data: ExceptionRecordsUpdateParam) => {
    return inspectionRequest.put<BaseResponse<object>>(`/exceptionRecords/update`, { data });
  },
  /**
   * @param id 巡检异常记录id
   * @returns 删除结果 || null
   * @description deleteById
   */
  deleteById: async (id: string) => {
    return inspectionRequest.delete<BaseResponse<object>>(`/exceptionRecords/deleteById`, {
      params: { id },
    });
  },
  /**
   * @param data {id:''} 巡检异常记录id
   * @returns 查询结果 || null
   * @description 根据id查询巡检异常记录信息
   */
  getDetailById: async (data: { id: string }) => {
    return inspectionRequest.get<BaseResponse<ExceptionRecordsVo>>(`/exceptionRecords/getById`, {
      params: data,
    });
  },
  /**
   * @param data PageQueryParam 分页查询参数
   * @returns  巡检异常记录信息列表 || null
   * @description 获取列表
   */
  queryByPage: async (data: ExceptionRecordsPageQueryParam) => {
    return inspectionRequest.post<ExceptionRecordsPageResponse>(`/exceptionRecords/queryByPage`, {
      data,
    });
  },
  /**
   * @param data PageQueryParam 分页查询参数
   * @returns  巡检异常记录信息导出文件 || null
   * @description 导出列表
   */
  exportByPage: async (data: ExceptionRecordsPageQueryParam) => {
    return new Promise((resolve) => {
      downloadBlob('/exceptionRecords/exportByPage', {
        method: 'POST',
        data,
      }).finally(() => {
        resolve('finally');
      });
    });
  },
};
