import { QueryParams } from '@/types/statistics';
import { <PERSON><PERSON>, But<PERSON> } from 'antd';
import React, { useEffect, useRef, useMemo } from 'react';
import YTHList, { ActionType, IYTHColumnProps } from 'yth-ui/es/components/list';
import { PlanPageResponse, PlanQueryParam, PlanQueryParamFilter } from '@/types/inspection/plan';
import PlanApi from '@/service/inspection/planApi';
import dicParams from '@/pages/InspectionPlan/dicParams';
import baseApi from '@/service/baseApi';
import style from '../statistics.module.less';

/**
 * @description 弹窗参数类型定义
 */
type PropsTypes = {
  /** 弹窗传入的数据 */
  searchParams: QueryParams;
  /** 弹窗是否可见 */
  visible: boolean;
  /** 关闭弹窗的回调函数 */
  closeModal: () => void;
};

/**
 * @description 查看 或新增 modal
 * @param PropsTypes PropsTypes
 */
const PlanListDialog: React.FC<PropsTypes> = ({ searchParams, visible, closeModal = () => {} }) => {
  // 使用 useMemo 确保 listAction 只创建一次
  const listAction: ActionType = useMemo(() => YTHList.createAction(), []);
  const listActionRef: React.MutableRefObject<ActionType | undefined> =
    useRef<ActionType>(listAction);

  // 表格列配置
  const columns: IYTHColumnProps[] = useMemo(
    () => [
      { dataIndex: 'serialNo', title: '序号', width: 80, display: false },
      {
        dataIndex: 'planCode',
        title: '计划编码',
        query: true,
        display: true,
        componentName: 'Input',
        componentProps: {
          placeholder: '请输入',
        },
      },
      {
        dataIndex: 'planName',
        title: '计划名称',
        query: true,
        display: true,
        componentName: 'Input',
        componentProps: {
          placeholder: '请输入',
        },
      },
      {
        dataIndex: 'inspectionMethod',
        title: '巡检方式',
        query: true,
        display: true,
        componentName: 'Selector',
        componentProps: {
          request: async () => {
            return baseApi.getDictionary(dicParams.INSPECTION_METHOD);
          },
          p_props: {
            placeholder: '请选择',
          },
        },
        render: (_, record) => {
          return record.inspectionMethodText || '-';
        },
      },
      {
        dataIndex: 'planType',
        title: '计划类型',
        query: true,
        display: true,
        componentName: 'Selector',
        componentProps: {
          request: async () => {
            return baseApi.getDictionary(dicParams.PLAN_TYPE);
          },
          p_props: {
            placeholder: '请选择',
          },
        },
        render: (_, record) => {
          return record.planTypeText || '-';
        },
      },
      {
        dataIndex: 'frequencyNumber',
        title: '周期',
        width: 130,
        query: false,
        display: true,
        render: (_, record) => {
          if (record.planType && record.planType === 'A27A02A01') {
            return `每${record.frequencyNumber}${record.executionFrequencyText || ''}执行一次`;
          }
          if (record.planType && record.planType === 'A27A02A02') {
            return '单次';
          }
          return '-';
        },
      },
      {
        dataIndex: 'executionFrequency',
        title: '单位',
        query: true,
        width: 0,
        display: true,
        componentName: 'Selector',
        componentProps: {
          request: async () => {
            return baseApi.getDictionary(dicParams.EXECUTION_FREQUENCY);
          },
          p_props: {
            placeholder: '请选择',
          },
        },
        render: (_, record) => {
          return record.executionFrequencyText || '-';
        },
      },
      {
        dataIndex: 'taskDate',
        title: '初次巡检时间',
        query: false,
        display: true,
        width: 130,
        componentName: 'Input',
        componentProps: {
          placeholder: '请输入',
        },
        render: (_, record) => {
          let result: string = record.validEndDate ? `${record.validStartDate}` : '';
          result += ' ';
          result += record.taskStartDate ? `${record.taskStartDate}` : '';
          return result;
        },
      },
      {
        dataIndex: 'isUsed',
        title: '计划状态',
        query: true,
        display: true,
        componentName: 'Selector',
        componentProps: {
          request: () => {
            return [
              { code: '1', text: '启用' },
              { code: '0', text: '停用' },
            ];
          },
          p_props: {
            placeholder: '请选择',
          },
        },
        render: (val: string) => {
          return String(val) === '1' ? '启用' : '停用';
        },
      },
      {
        dataIndex: 'isRemind',
        title: '是否短信提醒',
        query: false,
        display: true,
        componentName: 'Selector',
        componentProps: {
          request: () => {
            return [
              { code: '1', text: '是' },
              { code: '0', text: '否' },
            ];
          },
          p_props: {
            placeholder: '请选择',
          },
        },
        render: (val: string) => {
          return val === '1' ? '是' : '否';
        },
      },
      {
        dataIndex: 'directorUserName',
        title: '负责人',
        query: true,
        display: true,
        componentName: 'Input',
        componentProps: {
          placeholder: '请输入',
        },
      },
      {
        dataIndex: 'createDate',
        title: '创建时间',
        width: 120,
        query: false,
        display: true,
      },
    ],
    [],
  );

  // 处理弹窗关闭的回调函数
  const handleCloseModal: () => void = () => {
    closeModal();
  };

  // 加载数据
  useEffect(() => {}, [searchParams, visible]);

  /** 处理查询参数：从选择器数组中提取第一个选项的 code 值 */
  const handleFilter: (filter: PlanQueryParamFilter) => PlanQueryParam = (
    filter: PlanQueryParamFilter,
  ): PlanQueryParam => {
    return {
      // Input 组件直接取值
      planCode: filter.planCode,
      planName: filter.planName,
      directorUserName: filter.directorUserName,
      // Selector 组件从选中数组的第一个对象中提取 code
      inspectionMethod: filter.inspectionMethod?.[0]?.code,
      planType: filter.planType?.[0]?.code,
      executionFrequency: filter.executionFrequency?.[0]?.code,
      isUsed: filter.isUsed?.[0]?.code,
    };
  };
  return (
    <Modal
      width="80%"
      title="计划列表"
      style={{ top: 30 }}
      visible={visible}
      destroyOnClose
      onCancel={handleCloseModal}
      maskClosable={false}
      footer={[
        <Button key="cancel" onClick={handleCloseModal} className={style['reset-btn']}>
          取消
        </Button>,
      ]}
    >
      <YTHList
        defaultQuery={{}}
        code="InspectionPlanList"
        action={listAction}
        actionRef={listActionRef}
        showRowSelection={false}
        extraOperation={[]}
        operation={[]}
        listKey="id"
        request={async (filter, pagination) => {
          try {
            const resData: PlanPageResponse = await PlanApi.queryByPage({
              aescs: [],
              descs: [],
              condition: handleFilter(filter),
              currentPage: pagination.current,
              pageSize: pagination.pageSize,
            });
            if (resData.code && resData.code === 200) {
              resData.data.forEach((_, index) => {
                resData.data[index].serialNo =
                  (pagination.current - 1) * pagination.pageSize + index + 1;
              });
              return {
                data: resData.data,
                total: resData.total,
                success: true,
              };
            }
            return {
              data: [],
              total: 0,
              success: false,
            };
          } catch {
            return {
              data: [],
              total: 0,
              success: false,
            };
          }
        }}
        columns={columns}
      />
    </Modal>
  );
};

export default PlanListDialog;
