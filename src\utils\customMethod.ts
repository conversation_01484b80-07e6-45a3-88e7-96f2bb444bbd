import expression from './expression';

// 手机号校验
export const validatePhone: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.mobilePhone.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('手机号格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};
// 移动电话或座机校验
export const validateMobileOrLinePhone: (v: string) => Promise<void> = (
  v: string,
): Promise<void> => {
  if (v) {
    const res: boolean = expression.mobileOrLinePhone.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('移动电话或座机格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};

// 身份证号校验
export const validateIDCard: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.IDCard.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('身份证号码 格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};
// 道路运输许可证校验
export const validateTransportLicense: (v: string) => Promise<void> = (
  v: string,
): Promise<void> => {
  if (v) {
    const res: boolean = String(v).length === 12;
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('道路运输许可证 格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};

// 统一社会信用代码 校验
export const validateCredit: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.credit.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('统一社会信用代码 格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};

// 邮箱 校验
export const validateEmail: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.email.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('邮箱 格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};

// 车牌号校验
export const validatePlate: (v: string) => Promise<void> = (v: string): Promise<void> => {
  if (v) {
    const res: boolean = expression.carPlate.test(v);
    if (res) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('车牌号格式不正确！'));
  }
  return Promise.reject(new Error('请输入'));
};
