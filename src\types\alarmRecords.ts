import { PageQueryParam, PageResponse } from '@/types/common';

/* 巡检报警信息记录表结果VO */
export interface AlarmRecordsResultVo {
  /* 主键id */
  id?: string;
  /* 报警发生时间 */
  alarmTime?: string; // 前端通常使用字符串格式表示日期时间
  /* 报警描述 */
  alarmDescribe?: string;
  /* 上报设备id */
  reportDeviceId?: string;
  /* 上报设备编码 */
  reportDeviceCode?: string;
  /* 上报设备名称 */
  reportDeviceName?: string;
  /* 任务id */
  taskId?: string;
  /* 任务名称 */
  taskName?: string;
  /* 处置状态 0：未处置 1：已处置 2: 无需处置 */
  handleStatus?: number;
  /* 处置人 */
  handleUserId?: string;
  /* 处置人姓名 */
  handleUserName?: string;
  /* 处置方式 */
  handleMethod?: string;
  /* 处置时间 */
  handleTime?: string; // 前端通常使用字符串格式表示日期时间
  /* 创建时间 */
  createDate?: string; // 前端通常使用字符串格式表示日期时间
  /* 更新时间 */
  updateDate?: string; // 前端通常使用字符串格式表示日期时间
  /* 告警来源 */
  alarmSource?: string;
  /* 告警附件 */
  alarmFiles?: string;
}
/**
 * 异常记录 查询参数
 */
export interface AlarmRecordsQueryParam {
  reportDeviceCode?: string;
  alarmSource?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  handleStatus?:
    | string
    | { code?: string; text?: string; id?: string; value?: string; lable?: string }[];
  alarmTimeStart?: string;
  alarmTimeEnd?: string;
  startDate_end?: string;
  startDate_start?: string;
}

/**
 * @description 分页返回结果
 * @param data 分页查询参数
 */
export interface AlarmRecordsPageResponse extends PageResponse<AlarmRecordsResultVo> {
  data: AlarmRecordsResultVo[];
}

/**
 * @description 分页查询参数
 * @param data 分页查询参数
 */
export interface AlarmRecordsPageQueryParam extends PageQueryParam<AlarmRecordsQueryParam> {
  condition: AlarmRecordsQueryParam;
}
