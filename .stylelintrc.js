module.exports = {
  extends: ['stylelint-config-standard', 'stylelint-config-standard-less'],
  overrides: [
    {
      files: ['**/*.less', '**/*.module.less'],
      customSyntax: 'postcss-less',
    },
  ],
  plugins: [
    'stylelint-less', // 支持 Less 语法
  ],
  rules: {
    // 格式
    'color-hex-length': 'short', // #fff 代替 #ffffff
    'unit-no-unknown': true, // 禁止未知的单位

    // 空内容/重复
    'block-no-empty': true, // 禁止空的块
    'comment-no-empty': true, // 禁止空的注释
    'no-empty-source': null, // 空文件
    'no-duplicate-selectors': true,
    'at-rule-no-deprecated': true,
    'declaration-block-no-duplicate-custom-properties': true, // 禁止重复的自定义属性
    'declaration-block-no-duplicate-properties': [
      true,
      { ignore: ['consecutive-duplicates'] }, // 忽略连续重复的属性
    ], // 禁止重复的属性
    'font-family-no-duplicate-names': true, // 禁止重复的字体名称
    'no-duplicate-at-import-rules': true, // 禁止重复的 @import 规则
    'no-duplicate-selectors': true, // 禁止重复的选择器

    'color-no-invalid-hex': true, // 禁止无效的十六进制颜色
    'function-calc-no-unspaced-operator': true, // 禁止 calc() 函数中的未间隔运算符
    'media-query-no-invalid': true, // 禁止无效的媒体查询
    'at-rule-descriptor-no-unknown': true, // 禁止未知的 at-rule 描述符
    'at-rule-descriptor-value-no-unknown': true, // 禁止未知的 at-rule 描述符值
    'declaration-property-value-no-unknown': true, // 禁止未知的声明属性值
    'function-no-unknown': true, // 禁止未知的函数
    'length-zero-no-unit': true, // 禁止零长度单位

    'color-function-notation': null,
    'color-function-alias-notation': null, // 允许使用rgba
    'alpha-value-notation': 'number', // 允许小数
    'selector-class-pattern': '^[a-zA-Z0-9-]+$', // 允许大小写
  },
};
