/* 统计页面 */
.car-access-record-page {
  padding-top: 3px;
  background-color: #f2f2f2;

  .car-access-record-search-box {
    /* 搜索样式 */
    padding: 0 8px;
    margin: 8px;
    background-color: #fff;

    :global(.ant-form-item) {
      margin-bottom: 0 !important;
    }
  }

  .sum-card-box {
    /* 合计卡片容器 */
    padding: 0;
    margin: 0 8px;

    .sum-card {
      padding: 10px;
      margin-right: 10px;
      background-color: #fff;
      border-radius: 4px;

      .sum-number {
        font-size: 20px;
        font-weight: bold;
        line-height: 40px;
        color: #2196f3;
      }

      .sum-describe {
        font-size: 12px;
        line-height: 30px;
      }

      .img-col {
        text-align: center;

        :global(.ant-image-img) {
          height: 40px;
        }
      }
    }

    .sum-card.last {
      margin-right: 0;
    }
  }

  .trend-card-box {
    /* 趋势分析卡片容器 */
    padding: 0;
    margin-top: 10px ;

    :global(.ant-card-body) {
      padding-top: 10px;
    }
  }

  .car-access-record-table-box{
    :global(.yth-list-pc-extra){
      padding: 0;
    }

    .placeholder-input::placeholder {
      font-size: 12px;
    }
  }
}

/* 饼图卡片容器 */
.pie-card-box {
  padding: 0 0 10px;
  margin: 0 8px 8px;

  .pie-card {
    /* 饼图容器 */
    padding: 10px;
    margin-right: 10px;
    background-color: #fff;
    position: relative;

    .view-more-btn{
      position: absolute;
      right: 0;
      top: 0;
      font-size: 12px;
    }

    .pie-chart {
      /* 饼图 */
      width: 100%;
      height: 350px;
    }
  }

  .pie-card.last {
    margin-right: 0;
  }
}
