import React, { useEffect, useState } from 'react';
import { Row, Col, List, Empty } from 'antd';
import VideoPlayer from '@/components/VideoPlayer/VideoPlayer';

type Props = {
  videos: string;
};

// 视频数据类型
interface Video {
  deviceCode?: string;
  deviceName?: string;
  url?: string;
}
const VideoPlayerList: React.FC<Props> = ({
  videos, // 路线数据
}) => {
  // 视频列表
  const [videoList, setVideoList] = useState<Video[]>([]);
  // 当前播放的视频
  const [currentVideo, setCurrentVideo] = useState<Video>();

  const handleVideoChange: (video: Video) => void = (video: Video) => {
    setCurrentVideo(video);
  };

  useEffect(() => {
    setVideoList([]);
    try {
      if (videos) {
        // videos 是视频数据的 JSON 字符串
        setVideoList(JSON.parse(videos));
        handleVideoChange(videoList[0]);
      }
    } catch {
      // 解析失败时设置为空数组
      setVideoList([]);
    }
  }, [videos]);

  return (
    <div>
      {videoList.length === 0 && (
        <Empty
          style={{
            width: '100%',
            margin: '20px',
          }}
          imageStyle={{
            height: 60,
          }}
          description={<span>无巡检视频数据</span>}
        />
      )}
      {videoList.length > 0 && (
        <Row gutter={16}>
          <Col span={3}>
            <List
              dataSource={videoList}
              renderItem={(item) => (
                <List.Item onClick={() => handleVideoChange(item)}>
                  {item.deviceName || item.deviceCode}
                </List.Item>
              )}
            />
          </Col>
          <Col span={21}>
            <VideoPlayer options={{ sources: [{ src: currentVideo?.url, type: 'video/mp4' }] }} />
          </Col>
        </Row>
      )}
    </div>
  );
};

export default VideoPlayerList;
